# Template Routes Documentation

This document provides a comprehensive overview of all template-related API endpoints in the Cinepanda application.

## Base URL
All template routes are prefixed with `/api/templates` and require authentication.

## Template Types
The system supports two types of templates:
- **Regular Templates**: Standard user-created templates (`isPredefined: false`)
- **Predefined Templates**: System-defined templates for common use cases (`isPredefined: true`)

## Authentication & Authorization
- **Authentication**: All routes require a valid JWT token (protected by `protect` middleware)
- **Authorization**: Routes use role-based access control with the following roles:
  - `admin`: Full access to all operations
  - `manager`: Can create, read, and update templates and items
  - `user`: Read-only access to items

## Response Format
All endpoints follow a consistent response format:

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "data": { /* optional error details */ }
}
```

---

## Equipment Templates

### Base Route: `/api/templates/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/equipments` | admin, manager | Get all equipment templates |
| GET | `/api/templates/equipments?isPredefined=true` | admin, manager | Get predefined equipment templates |
| GET | `/api/templates/equipments?isPredefined=false` | admin, manager | Get regular equipment templates |
| GET | `/api/templates/equipments/predefined` | admin, manager | Get predefined equipment templates (dedicated endpoint) |
| POST | `/api/templates/equipments` | admin, manager | Create new regular equipment template |
| POST | `/api/templates/equipments/predefined` | admin, manager | Create new predefined equipment template |
| GET | `/api/templates/equipments/:id` | admin, manager | Get equipment template by ID |
| PUT | `/api/templates/equipments/:id` | admin, manager | Update equipment template |
| PUT | `/api/templates/equipments/:id?isPredefined=true` | admin, manager | Update predefined equipment template |
| DELETE | `/api/templates/equipments/:id` | admin | Delete equipment template |

#### POST `/api/templates/equipments` - Create Regular Equipment Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### POST `/api/templates/equipments/predefined` - Create Predefined Equipment Template

**Request Body:**
```json
{
  "name": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": true,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/equipments` - Get All Equipment Templates

**Query Parameters (optional):**
- `isPredefined`: `true` | `false` - Filter by template type

**Response (200) - Regular Templates:**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "templates": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "items": ["ObjectId array or populated items"],
        "totalAmount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### GET `/api/templates/equipments/predefined` - Get Predefined Equipment Templates

**Response (200) - Predefined Templates:**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "equipments": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "items": ["ObjectId array or populated items"],
        "totalAmount": "number",
        "isPredefined": true,
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### PUT `/api/templates/equipments/:id` - Update Equipment Template

**Query Parameters (optional):**
- `isPredefined`: `true` | `false` - Specify template type for validation

**Request Body (all fields optional):**

For Regular Templates:
```json
{
  "templateName": "string (2-100 chars)",
  "description": "string",
  "items": ["ObjectId array"]
}
```

For Predefined Templates:
```json
{
  "name": "string (2-100 chars)",
  "description": "string",
  "items": ["ObjectId array"]
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment template updated successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["populated items array"],
      "totalAmount": "number",
      "isPredefined": "boolean",
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Service Templates

### Base Route: `/api/templates/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/services` | admin, manager | Get all service templates |
| GET | `/api/templates/services?isPredefined=true` | admin, manager | Get predefined service templates |
| GET | `/api/templates/services?isPredefined=false` | admin, manager | Get regular service templates |
| GET | `/api/templates/services/predefined` | admin, manager | Get predefined service templates (dedicated endpoint) |
| POST | `/api/templates/services` | admin, manager | Create new regular service template |
| POST | `/api/templates/services/predefined` | admin, manager | Create new predefined service template |
| GET | `/api/templates/services/:id` | admin, manager | Get service template by ID |
| PUT | `/api/templates/services/:id` | admin, manager | Update service template |
| PUT | `/api/templates/services/:id?isPredefined=true` | admin, manager | Update predefined service template |
| DELETE | `/api/templates/services/:id` | admin | Delete service template |

#### POST `/api/templates/services` - Create Regular Service Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional, maps to services field)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Service template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "services": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### POST `/api/templates/services/predefined` - Create Predefined Service Template

**Request Body:**
```json
{
  "name": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional, maps to services field)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Service template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "services": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": true,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/services` - Get All Service Templates

**Query Parameters (optional):**
- `isPredefined`: `true` | `false` - Filter by template type

**Response (200) - Regular Templates:**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "templates": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "services": ["ObjectId array or populated services"],
        "totalAmount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### GET `/api/templates/services/predefined` - Get Predefined Service Templates

**Response (200) - Predefined Templates:**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "services": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "services": ["ObjectId array or populated services"],
        "totalAmount": "number",
        "isPredefined": true,
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Accessory Templates

### Base Route: `/api/templates/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/accessories` | admin, manager | Get all accessory templates |
| GET | `/api/templates/accessories?isPredefined=true` | admin, manager | Get predefined accessory templates |
| GET | `/api/templates/accessories?isPredefined=false` | admin, manager | Get regular accessory templates |
| GET | `/api/templates/accessories/predefined` | admin, manager | Get predefined accessory templates (dedicated endpoint) |
| POST | `/api/templates/accessories` | admin, manager | Create new regular accessory template |
| POST | `/api/templates/accessories/predefined` | admin, manager | Create new predefined accessory template |
| GET | `/api/templates/accessories/:id` | admin, manager | Get accessory template by ID |
| PUT | `/api/templates/accessories/:id` | admin, manager | Update accessory template |
| PUT | `/api/templates/accessories/:id?isPredefined=true` | admin, manager | Update predefined accessory template |
| DELETE | `/api/templates/accessories/:id` | admin | Delete accessory template |

#### POST `/api/templates/accessories` - Create Accessory Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Accessory template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Installation Templates

### Base Route: `/api/templates/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/installations` | admin, manager | Get all installation templates |
| GET | `/api/templates/installations?isPredefined=true` | admin, manager | Get predefined installation templates |
| GET | `/api/templates/installations?isPredefined=false` | admin, manager | Get regular installation templates |
| GET | `/api/templates/installations/predefined` | admin, manager | Get predefined installation templates (dedicated endpoint) |
| POST | `/api/templates/installations` | admin, manager | Create new regular installation template |
| POST | `/api/templates/installations/predefined` | admin, manager | Create new predefined installation template |
| GET | `/api/templates/installations/:id` | admin, manager | Get installation template by ID |
| PUT | `/api/templates/installations/:id` | admin, manager | Update installation template |
| PUT | `/api/templates/installations/:id?isPredefined=true` | admin, manager | Update predefined installation template |
| DELETE | `/api/templates/installations/:id` | admin | Delete installation template |

#### POST `/api/templates/installations` - Create Installation Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Installation template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Equipment Items

### Base Route: `/api/templates/items/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/equipments` | admin, manager, user | Get all equipment items |
| POST | `/api/templates/items/equipments` | admin, manager | Create new equipment item |
| GET | `/api/templates/items/equipments/search` | admin, manager, user | Search equipment items |
| GET | `/api/templates/items/equipments/:id` | admin, manager, user | Get equipment item by ID |
| PUT | `/api/templates/items/equipments/:id` | admin, manager | Update equipment item |
| DELETE | `/api/templates/items/equipments/:id` | admin, manager | Delete equipment item |

#### POST `/api/templates/items/equipments` - Create Equipment Item

**Request Body:**
```json
{
  "itemName": "string (required)",
  "specification": "string (optional)",
  "quantity": "number (required, min: 1)",
  "quantityType": "individual | set (required)",
  "price": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment item created successfully",
    "item": {
      "_id": "ObjectId",
      "itemName": "string",
      "specification": "string",
      "quantity": "number",
      "quantityType": "individual | set",
      "price": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/equipments` - Get All Equipment Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "itemName": "string",
        "specification": "string",
        "quantity": "number",
        "quantityType": "individual | set",
        "price": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### GET `/api/templates/items/equipments/search?query=searchTerm` - Search Equipment Items

**Query Parameters:**
- `query`: string (required, min: 1 character)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": ["array of matching equipment items"]
  }
}
```

---

## Service Items

### Base Route: `/api/templates/items/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/services` | admin, manager, user | Get all service items |
| POST | `/api/templates/items/services` | admin, manager | Create new service item |
| GET | `/api/templates/items/services/search` | admin, manager, user | Search service items |
| GET | `/api/templates/items/services/:id` | admin, manager, user | Get service item by ID |
| PUT | `/api/templates/items/services/:id` | admin, manager | Update service item |
| DELETE | `/api/templates/items/services/:id` | admin, manager | Delete service item |

#### POST `/api/templates/items/services` - Create Service Item

**Request Body:**
```json
{
  "serviceName": "string (required, 3-100 chars)",
  "areaInSquareFeet": "number (required, min: 0)",
  "pricePerSquareFoot": "number (required, min: 0)",
  "totalPrice": "number (optional, calculated automatically)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Service item created successfully",
    "item": {
      "_id": "ObjectId",
      "serviceName": "string",
      "areaInSquareFeet": "number",
      "pricePerSquareFoot": "number",
      "totalPrice": "number (calculated: areaInSquareFeet * pricePerSquareFoot)",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/services` - Get All Service Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "serviceName": "string",
        "areaInSquareFeet": "number",
        "pricePerSquareFoot": "number",
        "totalPrice": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Accessory Items

### Base Route: `/api/templates/items/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/accessories` | admin, manager, user | Get all accessory items |
| POST | `/api/templates/items/accessories` | admin, manager | Create new accessory item |
| GET | `/api/templates/items/accessories/search` | admin, manager, user | Search accessory items |
| GET | `/api/templates/items/accessories/:id` | admin, manager, user | Get accessory item by ID |
| PUT | `/api/templates/items/accessories/:id` | admin, manager | Update accessory item |
| DELETE | `/api/templates/items/accessories/:id` | admin, manager | Delete accessory item |

#### POST `/api/templates/items/accessories` - Create Accessory Item

**Request Body:**
```json
{
  "itemName": "string (required)",
  "quantity": "number (required, min: 1)",
  "quantityType": "individual | set (required)",
  "amount": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Accessory item created successfully",
    "item": {
      "_id": "ObjectId",
      "itemName": "string",
      "quantity": "number",
      "quantityType": "individual | set",
      "amount": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/accessories` - Get All Accessory Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "itemName": "string",
        "quantity": "number",
        "quantityType": "individual | set",
        "amount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Installation Items

### Base Route: `/api/templates/items/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/installations` | admin, manager, user | Get all installation items |
| POST | `/api/templates/items/installations` | admin, manager | Create new installation item |
| GET | `/api/templates/items/installations/search` | admin, manager, user | Search installation items |
| GET | `/api/templates/items/installations/:id` | admin, manager, user | Get installation item by ID |
| PUT | `/api/templates/items/installations/:id` | admin, manager | Update installation item |
| DELETE | `/api/templates/items/installations/:id` | admin, manager | Delete installation item |

#### POST `/api/templates/items/installations` - Create Installation Item

**Request Body:**
```json
{
  "name": "string (required, 3-100 chars)",
  "price": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Installation item created successfully",
    "item": {
      "_id": "ObjectId",
      "name": "string",
      "price": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/installations` - Get All Installation Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "name": "string",
        "price": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Common Error Responses

### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "error": "Validation Error",
  "data": {
    "messages": [
      "Item name is required",
      "Price cannot be negative"
    ]
  }
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Access denied. No token provided."
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "Access denied. Insufficient permissions."
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Equipment template not found"
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": "An equipment item with this name already exists"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Error creating equipment item",
  "data": { /* error details */ }
}
```

---

## Notes

1. **Templates vs Items**:
   - Templates are the main categories/types (equipment, services, accessories, installations)
   - Items are the specific instances within each template category

2. **Predefined vs Regular Templates**:
   - **Regular Templates**: User-created templates with `isPredefined: false`
   - **Predefined Templates**: System-defined templates with `isPredefined: true`
   - Predefined templates use `name` field in requests but store as `templateName` internally
   - Different validation schemas are used for predefined vs regular templates

3. **Query Parameter Support**:
   - Use `?isPredefined=true` to filter for predefined templates only
   - Use `?isPredefined=false` to filter for regular templates only
   - Omit parameter to get all templates regardless of type

4. **Dedicated Predefined Endpoints**:
   - `/predefined` endpoints automatically set `isPredefined=true`
   - These endpoints return data in format expected by frontend (e.g., `equipments` instead of `templates`)

5. **Service Template Special Handling**:
   - Service templates use `services` field internally instead of `items`
   - API requests still use `items` field which gets mapped to `services` automatically

6. **Search Functionality**:
   - All item categories support search functionality via `/search` endpoint
   - Search is available to all authenticated users (admin, manager, user)
   - Search query parameter: `?query=searchTerm` (minimum 1 character)

7. **Role Permissions**:
   - **Admin**: Full CRUD access to all templates and items
   - **Manager**: Can create, read, and update templates and items (cannot delete templates)
   - **User**: Read-only access to items only

8. **Authentication**: All routes require a valid JWT token in the Authorization header:
   ```
   Authorization: Bearer <your-jwt-token>
   ```

9. **Data Validation**:
   - All input data is validated using Zod schemas
   - String fields are automatically trimmed
   - Numeric fields have minimum value constraints
   - ObjectId fields are validated for proper format

10. **Automatic Calculations**:
    - Service items: `totalPrice` is automatically calculated as `areaInSquareFeet * pricePerSquareFoot`
    - Template `totalAmount` is calculated based on associated items

11. **Timestamps**: All entities include `createdAt` and `updatedAt` fields automatically managed by MongoDB

12. **Population**: Template GET requests can populate associated items for detailed responses

13. **Unique Constraints**: Item names must be unique within their respective categories

14. **Field Mapping for Predefined Templates**:
    - Request field `name` maps to database field `templateName`
    - Request field `items` maps to database field `services` for service templates
    - This ensures consistency between frontend expectations and database schema
