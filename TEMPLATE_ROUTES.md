# Template Routes Documentation

This document provides a comprehensive overview of all template-related API endpoints in the Cinepanda application.

## Base URL
All template routes are prefixed with `/api/templates` and require authentication.

## Authentication & Authorization
- **Authentication**: All routes require a valid JWT token (protected by `protect` middleware)
- **Authorization**: Routes use role-based access control with the following roles:
  - `admin`: Full access to all operations
  - `manager`: Can create, read, and update templates and items
  - `user`: Read-only access to items

## Response Format
All endpoints follow a consistent response format:

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "data": { /* optional error details */ }
}
```

---

## Equipment Templates

### Base Route: `/api/templates/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/equipments` | admin, manager | Get all equipment templates |
| POST | `/api/templates/equipments` | admin, manager | Create new equipment template |
| GET | `/api/templates/equipments/:id` | admin, manager | Get equipment template by ID |
| PUT | `/api/templates/equipments/:id` | admin, manager | Update equipment template |
| DELETE | `/api/templates/equipments/:id` | admin | Delete equipment template |

#### POST `/api/templates/equipments` - Create Equipment Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/equipments` - Get All Equipment Templates

**Response (200):**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "items": ["ObjectId array or populated items"],
        "totalAmount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### PUT `/api/templates/equipments/:id` - Update Equipment Template

**Request Body (all fields optional):**
```json
{
  "templateName": "string (2-100 chars)",
  "description": "string",
  "items": ["ObjectId array"]
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment template updated successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["populated items array"],
      "totalAmount": "number",
      "isPredefined": "boolean",
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Service Templates

### Base Route: `/api/templates/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/services` | admin, manager | Get all service templates |
| POST | `/api/templates/services` | admin, manager | Create new service template |
| GET | `/api/templates/services/:id` | admin, manager | Get service template by ID |
| PUT | `/api/templates/services/:id` | admin, manager | Update service template |
| DELETE | `/api/templates/services/:id` | admin | Delete service template |

#### POST `/api/templates/services` - Create Service Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Service template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "services": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/services` - Get All Service Templates

**Response (200):**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "_id": "ObjectId",
        "templateName": "string",
        "services": ["ObjectId array or populated services"],
        "totalAmount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Accessory Templates

### Base Route: `/api/templates/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/accessories` | admin, manager | Get all accessory templates |
| POST | `/api/templates/accessories` | admin, manager | Create new accessory template |
| GET | `/api/templates/accessories/:id` | admin, manager | Get accessory template by ID |
| PUT | `/api/templates/accessories/:id` | admin, manager | Update accessory template |
| DELETE | `/api/templates/accessories/:id` | admin | Delete accessory template |

#### POST `/api/templates/accessories` - Create Accessory Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Accessory template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Installation Templates

### Base Route: `/api/templates/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/installations` | admin, manager | Get all installation templates |
| POST | `/api/templates/installations` | admin, manager | Create new installation template |
| GET | `/api/templates/installations/:id` | admin, manager | Get installation template by ID |
| PUT | `/api/templates/installations/:id` | admin, manager | Update installation template |
| DELETE | `/api/templates/installations/:id` | admin | Delete installation template |

#### POST `/api/templates/installations` - Create Installation Template

**Request Body:**
```json
{
  "templateName": "string (required, 2-100 chars)",
  "description": "string (optional)",
  "items": ["ObjectId array (optional)"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Installation template created successfully",
    "template": {
      "_id": "ObjectId",
      "templateName": "string",
      "items": ["ObjectId array"],
      "totalAmount": 0,
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

---

## Equipment Items

### Base Route: `/api/templates/items/equipments`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/equipments` | admin, manager, user | Get all equipment items |
| POST | `/api/templates/items/equipments` | admin, manager | Create new equipment item |
| GET | `/api/templates/items/equipments/search` | admin, manager, user | Search equipment items |
| GET | `/api/templates/items/equipments/:id` | admin, manager, user | Get equipment item by ID |
| PUT | `/api/templates/items/equipments/:id` | admin, manager | Update equipment item |
| DELETE | `/api/templates/items/equipments/:id` | admin, manager | Delete equipment item |

#### POST `/api/templates/items/equipments` - Create Equipment Item

**Request Body:**
```json
{
  "itemName": "string (required)",
  "specification": "string (optional)",
  "quantity": "number (required, min: 1)",
  "quantityType": "individual | set (required)",
  "price": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Equipment item created successfully",
    "item": {
      "_id": "ObjectId",
      "itemName": "string",
      "specification": "string",
      "quantity": "number",
      "quantityType": "individual | set",
      "price": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/equipments` - Get All Equipment Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "itemName": "string",
        "specification": "string",
        "quantity": "number",
        "quantityType": "individual | set",
        "price": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

#### GET `/api/templates/items/equipments/search?query=searchTerm` - Search Equipment Items

**Query Parameters:**
- `query`: string (required, min: 1 character)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": ["array of matching equipment items"]
  }
}
```

---

## Service Items

### Base Route: `/api/templates/items/services`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/services` | admin, manager, user | Get all service items |
| POST | `/api/templates/items/services` | admin, manager | Create new service item |
| GET | `/api/templates/items/services/search` | admin, manager, user | Search service items |
| GET | `/api/templates/items/services/:id` | admin, manager, user | Get service item by ID |
| PUT | `/api/templates/items/services/:id` | admin, manager | Update service item |
| DELETE | `/api/templates/items/services/:id` | admin, manager | Delete service item |

#### POST `/api/templates/items/services` - Create Service Item

**Request Body:**
```json
{
  "serviceName": "string (required, 3-100 chars)",
  "areaInSquareFeet": "number (required, min: 0)",
  "pricePerSquareFoot": "number (required, min: 0)",
  "totalPrice": "number (optional, calculated automatically)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Service item created successfully",
    "item": {
      "_id": "ObjectId",
      "serviceName": "string",
      "areaInSquareFeet": "number",
      "pricePerSquareFoot": "number",
      "totalPrice": "number (calculated: areaInSquareFeet * pricePerSquareFoot)",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/services` - Get All Service Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "serviceName": "string",
        "areaInSquareFeet": "number",
        "pricePerSquareFoot": "number",
        "totalPrice": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Accessory Items

### Base Route: `/api/templates/items/accessories`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/accessories` | admin, manager, user | Get all accessory items |
| POST | `/api/templates/items/accessories` | admin, manager | Create new accessory item |
| GET | `/api/templates/items/accessories/search` | admin, manager, user | Search accessory items |
| GET | `/api/templates/items/accessories/:id` | admin, manager, user | Get accessory item by ID |
| PUT | `/api/templates/items/accessories/:id` | admin, manager | Update accessory item |
| DELETE | `/api/templates/items/accessories/:id` | admin, manager | Delete accessory item |

#### POST `/api/templates/items/accessories` - Create Accessory Item

**Request Body:**
```json
{
  "itemName": "string (required)",
  "quantity": "number (required, min: 1)",
  "quantityType": "individual | set (required)",
  "amount": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Accessory item created successfully",
    "item": {
      "_id": "ObjectId",
      "itemName": "string",
      "quantity": "number",
      "quantityType": "individual | set",
      "amount": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/accessories` - Get All Accessory Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "itemName": "string",
        "quantity": "number",
        "quantityType": "individual | set",
        "amount": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Installation Items

### Base Route: `/api/templates/items/installations`

| Method | Endpoint | Access | Description |
|--------|----------|--------|-------------|
| GET | `/api/templates/items/installations` | admin, manager, user | Get all installation items |
| POST | `/api/templates/items/installations` | admin, manager | Create new installation item |
| GET | `/api/templates/items/installations/search` | admin, manager, user | Search installation items |
| GET | `/api/templates/items/installations/:id` | admin, manager, user | Get installation item by ID |
| PUT | `/api/templates/items/installations/:id` | admin, manager | Update installation item |
| DELETE | `/api/templates/items/installations/:id` | admin, manager | Delete installation item |

#### POST `/api/templates/items/installations` - Create Installation Item

**Request Body:**
```json
{
  "name": "string (required, 3-100 chars)",
  "price": "number (required, min: 0)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "message": "Installation item created successfully",
    "item": {
      "_id": "ObjectId",
      "name": "string",
      "price": "number",
      "isPredefined": false,
      "createdAt": "ISO date",
      "updatedAt": "ISO date"
    }
  }
}
```

#### GET `/api/templates/items/installations` - Get All Installation Items

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number",
    "items": [
      {
        "_id": "ObjectId",
        "name": "string",
        "price": "number",
        "isPredefined": "boolean",
        "createdAt": "ISO date",
        "updatedAt": "ISO date"
      }
    ]
  }
}
```

---

## Common Error Responses

### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "error": "Validation Error",
  "data": {
    "messages": [
      "Item name is required",
      "Price cannot be negative"
    ]
  }
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Access denied. No token provided."
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "Access denied. Insufficient permissions."
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Equipment template not found"
}
```

### 409 Conflict
```json
{
  "success": false,
  "error": "An equipment item with this name already exists"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Error creating equipment item",
  "data": { /* error details */ }
}
```

---

## Notes

1. **Templates vs Items**:
   - Templates are the main categories/types (equipment, services, accessories, installations)
   - Items are the specific instances within each template category

2. **Search Functionality**:
   - All item categories support search functionality via `/search` endpoint
   - Search is available to all authenticated users (admin, manager, user)
   - Search query parameter: `?query=searchTerm` (minimum 1 character)

3. **Role Permissions**:
   - **Admin**: Full CRUD access to all templates and items
   - **Manager**: Can create, read, and update templates and items (cannot delete templates)
   - **User**: Read-only access to items only

4. **Authentication**: All routes require a valid JWT token in the Authorization header:
   ```
   Authorization: Bearer <your-jwt-token>
   ```

5. **Data Validation**:
   - All input data is validated using Zod schemas
   - String fields are automatically trimmed
   - Numeric fields have minimum value constraints
   - ObjectId fields are validated for proper format

6. **Automatic Calculations**:
   - Service items: `totalPrice` is automatically calculated as `areaInSquareFeet * pricePerSquareFoot`
   - Template `totalAmount` is calculated based on associated items

7. **Timestamps**: All entities include `createdAt` and `updatedAt` fields automatically managed by MongoDB

8. **Population**: Template GET requests can populate associated items for detailed responses

9. **Unique Constraints**: Item names must be unique within their respective categories
