import { Router } from 'express';
import {
    getAllAccessoryItems,
    getAccessoryItemById,
    createAccessoryItem,
    updateAccessoryItem,
    deleteAccessoryItem,
    searchAccessoryItems,
  } from '../../../controllers/Templates/Items/accessoryItem.controller';
import { restrictTo } from '../../../middleware/auth.middleware';

const accessoryItemsRouter = Router();

accessoryItemsRouter.route('/')
    .get(restrictTo('admin', 'manager', 'user'), getAllAccessoryItems)
    .post(restrictTo('admin', 'manager'), createAccessoryItem);

accessoryItemsRouter.route('/search').get(restrictTo('admin', 'manager', 'user'), searchAccessoryItems);

accessoryItemsRouter.route('/:id')
    .get(restrictTo('admin', 'manager', 'user'), getAccessoryItemById)
    .put(restrictTo('admin', 'manager'), updateAccessoryItem)
    .delete(restrictTo('admin', 'manager'), deleteAccessoryItem);

export default accessoryItemsRouter;
