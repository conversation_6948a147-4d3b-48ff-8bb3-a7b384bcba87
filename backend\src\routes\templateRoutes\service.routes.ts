import { Router } from 'express';
import {
  getAllServiceTemplates,
  getServiceTemplateById,
  createServiceTemplate,
  updateServiceTemplate,
  deleteServiceTemplate,
} from '../../controllers/Templates/service.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const router = Router();

// Service templates routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllServiceTemplates)
  .post(restrictTo('admin', 'manager'), createServiceTemplate);

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getServiceTemplateById)
  .put(restrictTo('admin', 'manager'), updateServiceTemplate)
  .delete(restrictTo('admin'), deleteServiceTemplate);

export default router;
