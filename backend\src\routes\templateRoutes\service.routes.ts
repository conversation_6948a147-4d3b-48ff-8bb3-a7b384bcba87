import { Router } from 'express';
import {
  getAllServiceTemplates,
  getServiceTemplateById,
  createServiceTemplate,
  updateServiceTemplate,
  deleteServiceTemplate,
} from '../../controllers/Templates/service.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const router = Router();

// Service templates routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllServiceTemplates)
  .post(restrictTo('admin', 'manager'), createServiceTemplate);

// Predefined templates routes
router.route('/predefined')
  .get(restrictTo('admin', 'manager'), (req, res) => {
    req.query.isPredefined = 'true';
    getAllServiceTemplates(req, res);
  })
  .post(restrictTo('admin', 'manager'), (req, res) => {
    req.query.isPredefined = 'true';
    createServiceTemplate(req, res);
  });

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getServiceTemplateById)
  .put(restrictTo('admin', 'manager'), updateServiceTemplate)
  .delete(restrictTo('admin'), deleteServiceTemplate);

export default router;
