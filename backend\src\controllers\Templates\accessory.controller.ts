import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import AccessoryTemplate from '../../models/Templates/Accessory/AccessoryTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createAccessoryTemplateSchema, 
  updateAccessoryTemplateSchema, 
  templateIdSchema,
  createPredefinedAccessoryTemplateSchema,
  updatePredefinedAccessoryTemplateSchema
} from './templates.validation';

/**
 * @description Get all accessory templates
 * @route GET /api/templates/accessories?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const getAllAccessoryTemplates = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Build filter based on isPredefined query parameter
    let filter = {};
    if (isPredefined !== undefined) {
      filter = { isPredefined: isPredefined === 'true' };
    }

    const templates = await AccessoryTemplate.find(filter)
      .populate('items')
      .sort({ createdAt: -1 });

    // For predefined templates, return in the format expected by frontend
    if (isPredefined === 'true') {
      return sendSuccess(res, 200, {
        count: templates.length,
        accessories: templates,
      });
    }

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory templates', error);
  }
};

/**
 * @description Get a single accessory template by ID
 * @route GET /api/templates/accessories/:id
 * @access Protected (Admin, Manager)
 */
export const getAccessoryTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await AccessoryTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Accessory template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory template', error);
  }
};

/**
 * @description Create a new accessory template
 * @route POST /api/templates/accessories?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const createAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;
    const isCreatingPredefined = isPredefined === 'true';

    // Use appropriate validation schema based on isPredefined
    const validationSchema = isCreatingPredefined
      ? createPredefinedAccessoryTemplateSchema
      : createAccessoryTemplateSchema;

    const result = validationSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let templateData;
    if (isCreatingPredefined) {
      const { name, description, items = [] } = result.data as any;
      templateData = {
        templateName: name, // Map 'name' to 'templateName' for predefined
        description,
        items,
        isPredefined: true,
      };
    } else {
      const { templateName, description, items = [] } = result.data as any;
      templateData = {
        templateName,
        description,
        items,
        isPredefined: false,
      };
    }

    const newTemplate = await AccessoryTemplate.create(templateData);

    // Populate items for response
    const populatedTemplate = await AccessoryTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, {
      message: 'Accessory template created successfully',
      template: populatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating accessory template', error);
  }
};

/**
 * @description Update an accessory template by ID
 * @route PUT /api/templates/accessories/:id?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const updateAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;

    // Check if the template exists
    const existingTemplate = await AccessoryTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Accessory template not found');
    }

    // Use appropriate validation schema based on isPredefined or existing template
    const isUpdatingPredefined = isPredefined === 'true' || existingTemplate.isPredefined;
    const validationSchema = isUpdatingPredefined
      ? updatePredefinedAccessoryTemplateSchema
      : updateAccessoryTemplateSchema;

    const bodyResult = validationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let updateData;
    if (isUpdatingPredefined) {
      const { name, description, items } = bodyResult.data as any;
      updateData = {
        ...(name && { templateName: name }), // Map 'name' to 'templateName' for predefined
        ...(description !== undefined && { description }),
        ...(items && { items }),
      };
    } else {
      updateData = bodyResult.data;
    }

    const updatedTemplate = await AccessoryTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, {
      message: 'Accessory template updated successfully',
      template: updatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating accessory template', error);
  }
};

/**
 * @description Delete an accessory template by ID
 * @route DELETE /api/templates/accessories/:id
 * @access Protected (Admin)
 */
export const deleteAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await AccessoryTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Accessory template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Accessory template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting accessory template', error);
  }
};


