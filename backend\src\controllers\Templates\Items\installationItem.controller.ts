import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import InstallationItem from '../../../models/Templates/Installation/InstallationItem';
import { sendError, sendSuccess } from '../../../utils/responseHandlers';
import {
  createInstallationItemSchema,
  updateInstallationItemSchema,
  installationItemIdSchema,
  searchInstallationItemSchema,
} from './installationItem.validation';

/**
 * @description Get all installation items
 * @route GET /api/templates/installation-items
 * @access Protected (Admin, Manager)
 */
export const getAllInstallationItems = async (req: Request, res: Response) => {
  try {
    const items = await InstallationItem.find().sort({ name: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation items', error);
  }
};

/**
 * @description Get a single installation item by ID
 * @route GET /api/templates/installation-items/:id
 * @access Protected (Admin, Manager)
 */
export const getInstallationItemById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate installation item ID using Zod schema
    const result = installationItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid installation item ID', { messages });
    }

    const item = await InstallationItem.findById(id);
    
    if (!item) {
      return sendError(res, 404, 'Installation item not found');
    }

    return sendSuccess(res, 200, { item });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation item', error);
  }
};

/**
 * @description Create a new installation item
 * @route POST /api/templates/installation-items
 * @access Protected (Admin, Manager)
 */
export const createInstallationItem = async (req: Request, res: Response) => {
  try {
    // Validate request body using Zod schema
    const result = createInstallationItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { name, price } = result.data;

    // Check if installation item with the same name exists
    const existingItem = await InstallationItem.findOne({ name });
    if (existingItem) {
      return sendError(res, 400, 'An installation item with this name already exists');
    }

    const newItem = await InstallationItem.create({
      name,
      price,
    });

    return sendSuccess(res, 201, { 
      message: 'Installation item created successfully', 
      item: newItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error creating installation item', error);
  }
};

/**
 * @description Update an installation item by ID
 * @route PUT /api/templates/installation-items/:id
 * @access Protected (Admin, Manager)
 */
export const updateInstallationItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate installation item ID using Zod schema
    const idValidation = installationItemIdSchema.safeParse({ id });
    
    if (!idValidation.success) {
      const messages = idValidation.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid installation item ID', { messages });
    }

    // Validate request body using Zod schema
    const result = updateInstallationItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    // Check if the installation item exists
    const existingItem = await InstallationItem.findById(id);
    if (!existingItem) {
      return sendError(res, 404, 'Installation item not found');
    }

    const updateData = result.data;

    // Check if name is being updated and if it's already in use
    if (updateData.name && updateData.name !== existingItem.name) {
      const nameExists = await InstallationItem.findOne({ 
        name: updateData.name,
        _id: { $ne: id } // Exclude the current item from the search
      });
      
      if (nameExists) {
        return sendError(res, 400, 'Item name is already in use');
      }
    }

    const updatedItem = await InstallationItem.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return sendSuccess(res, 200, { 
      message: 'Installation item updated successfully', 
      item: updatedItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error updating installation item', error);
  }
};

/**
 * @description Delete an installation item by ID
 * @route DELETE /api/templates/installation-items/:id
 * @access Protected (Admin)
 */
export const deleteInstallationItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate installation item ID using Zod schema
    const result = installationItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid installation item ID', { messages });
    }

    const item = await InstallationItem.findByIdAndDelete(id);
    
    if (!item) {
      return sendError(res, 404, 'Installation item not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Installation item deleted successfully',
      itemId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting installation item', error);
  }
};

/**
 * @description Search installation items by name
 * @route GET /api/templates/installation-items/search
 * @access Protected (Admin, Manager)
 */
export const searchInstallationItems = async (req: Request, res: Response) => {
  try {
    // Validate search query using Zod schema
    const result = searchInstallationItemSchema.safeParse({ query: req.query.query });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    const { query } = result.data;

    const items = await InstallationItem.find({
      name: { $regex: query, $options: 'i' }
    }).sort({ name: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
      searchQuery: query
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error searching installation items', error);
  }
};

