import { Request, Response } from 'express';
import EquipmentItem from '../../../models/Templates/Equipments/EquipmentItem';
import { sendError, sendSuccess } from '../../../utils/responseHandlers';
import {
  createEquipmentItemSchema,
  updateEquipmentItemSchema,
  equipmentItemIdSchema,
  searchEquipmentItemSchema,
} from './equipmentItem.validation';

/**
 * @description Get all equipment items
 * @route GET /api/templates/equipment-items
 * @access Protected (Admin, Manager)
 */
export const getAllEquipmentItems = async (req: Request, res: Response) => {
  try {
    const items = await EquipmentItem.find().sort({ itemName: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching equipment items', error);
  }
};

/**
 * @description Get a single equipment item by ID
 * @route GET /api/templates/equipment-items/:id
 * @access Protected (Ad<PERSON>, Manager)
 */
export const getEquipmentItemById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate equipment item ID using Zod schema
    const result = equipmentItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid equipment item ID', { messages });
    }

    const item = await EquipmentItem.findById(id);
    
    if (!item) {
      return sendError(res, 404, 'Equipment item not found');
    }

    return sendSuccess(res, 200, { item });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching equipment item', error);
  }
};

/**
 * @description Create a new equipment item
 * @route POST /api/templates/equipment-items
 * @access Protected (Admin, Manager)
 */
export const createEquipmentItem = async (req: Request, res: Response) => {
  try {

    console.log('createEquipmentItem', req.body);

    // Validate request body using Zod schema
    const result = createEquipmentItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { itemName, specification, quantity, quantityType, price } = result.data;

    // Check if equipment item with the same name exists
    const existingItem = await EquipmentItem.findOne({ itemName });
    if (existingItem) {
      return sendError(res, 400, 'An equipment item with this name already exists');
    }

    const newItem = await EquipmentItem.create({
      itemName,
      specification,
      quantity,
      quantityType,
      price,
    });

    return sendSuccess(res, 201, { 
      message: 'Equipment item created successfully', 
      item: newItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error creating equipment item', error);
  }
};

/**
 * @description Update an equipment item by ID
 * @route PUT /api/templates/equipment-items/:id
 * @access Protected (Admin, Manager)
 */
export const updateEquipmentItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate equipment item ID using Zod schema
    const idValidation = equipmentItemIdSchema.safeParse({ id });
    
    if (!idValidation.success) {
      const messages = idValidation.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid equipment item ID', { messages });
    }

    // Validate request body using Zod schema
    const result = updateEquipmentItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    // Check if the equipment item exists
    const existingItem = await EquipmentItem.findById(id);
    if (!existingItem) {
      return sendError(res, 404, 'Equipment item not found');
    }

    const updateData = result.data;

    // Check if itemName is being updated and if it's already in use
    if (updateData.itemName && updateData.itemName !== existingItem.itemName) {
      const nameExists = await EquipmentItem.findOne({ 
        itemName: updateData.itemName,
        _id: { $ne: id } // Exclude the current item from the search
      });
      
      if (nameExists) {
        return sendError(res, 400, 'Item name is already in use');
      }
    }

    const updatedItem = await EquipmentItem.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return sendSuccess(res, 200, { 
      message: 'Equipment item updated successfully', 
      item: updatedItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error updating equipment item', error);
  }
};

/**
 * @description Delete an equipment item by ID
 * @route DELETE /api/templates/equipment-items/:id
 * @access Protected (Admin)
 */
export const deleteEquipmentItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate equipment item ID using Zod schema
    const result = equipmentItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid equipment item ID', { messages });
    }

    const item = await EquipmentItem.findByIdAndDelete(id);
    
    if (!item) {
      return sendError(res, 404, 'Equipment item not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Equipment item deleted successfully',
      itemId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting equipment item', error);
  }
};

/**
 * @description Search equipment items by name
 * @route GET /api/templates/equipment-items/search
 * @access Protected (Admin, Manager)
 */
export const searchEquipmentItems = async (req: Request, res: Response) => {
  try {
    // Validate search query using Zod schema
    const result = searchEquipmentItemSchema.safeParse({ query: req.query.query });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    const { query } = result.data;

    const items = await EquipmentItem.find({
      $or: [
        { itemName: { $regex: query, $options: 'i' } },
        { specification: { $regex: query, $options: 'i' } }
      ]
    }).sort({ itemName: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
      searchQuery: query
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error searching equipment items', error);
  }
};

