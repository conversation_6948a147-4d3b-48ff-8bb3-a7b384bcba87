import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import AccessoryItem from '../../../models/Templates/Accessory/AccessoryItem';
import { sendError, sendSuccess } from '../../../utils/responseHandlers';
import {
  createAccessoryItemSchema,
  updateAccessoryItemSchema,
  accessoryItemIdSchema,
  searchAccessoryItemSchema
} from './accessoryItem.validation';

/**
 * @description Get all accessory items
 * @route GET /api/templates/accessory-items
 * @access Protected (Admin, Manager)
 */
export const getAllAccessoryItems = async (req: Request, res: Response) => {
  try {
    const items = await AccessoryItem.find().sort({ itemName: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory items', error);
  }
};

/**
 * @description Get a single accessory item by ID
 * @route GET /api/templates/accessory-items/:id
 * @access Protected (Admin, Manager)
 */
export const getAccessoryItemById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate accessory item ID using Zod schema
    const result = accessoryItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid accessory item ID', { messages });
    }

    const item = await AccessoryItem.findById(id);
    
    if (!item) {
      return sendError(res, 404, 'Accessory item not found');
    }

    return sendSuccess(res, 200, { item });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory item', error);
  }
};

/**
 * @description Create a new accessory item
 * @route POST /api/templates/accessory-items
 * @access Protected (Admin, Manager)
 */
export const createAccessoryItem = async (req: Request, res: Response) => {
  try {
    // Validate request body using Zod schema
    const result = createAccessoryItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { itemName, quantity, quantityType, amount } = result.data;

    // Check if accessory item with the same name exists
    const existingItem = await AccessoryItem.findOne({ itemName });
    if (existingItem) {
      return sendError(res, 400, 'An accessory item with this name already exists');
    }

    const newItem = await AccessoryItem.create({
      itemName,
      quantity,
      quantityType,
      amount,
    });

    return sendSuccess(res, 201, { 
      message: 'Accessory item created successfully', 
      item: newItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error creating accessory item', error);
  }
};

/**
 * @description Update an accessory item by ID
 * @route PUT /api/templates/accessory-items/:id
 * @access Protected (Admin, Manager)
 */
export const updateAccessoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate accessory item ID using Zod schema
    const idValidation = accessoryItemIdSchema.safeParse({ id });
    
    if (!idValidation.success) {
      const messages = idValidation.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid accessory item ID', { messages });
    }

    // Validate request body using Zod schema
    const result = updateAccessoryItemSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    // Check if the accessory item exists
    const existingItem = await AccessoryItem.findById(id);
    if (!existingItem) {
      return sendError(res, 404, 'Accessory item not found');
    }

    const updateData = result.data;

    // Check if itemName is being updated and if it's already in use
    if (updateData.itemName && updateData.itemName !== existingItem.itemName) {
      const nameExists = await AccessoryItem.findOne({ 
        itemName: updateData.itemName,
        _id: { $ne: id } // Exclude the current item from the search
      });
      
      if (nameExists) {
        return sendError(res, 400, 'Item name is already in use');
      }
    }

    const updatedItem = await AccessoryItem.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return sendSuccess(res, 200, { 
      message: 'Accessory item updated successfully', 
      item: updatedItem 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error updating accessory item', error);
  }
};

/**
 * @description Delete an accessory item by ID
 * @route DELETE /api/templates/accessory-items/:id
 * @access Protected (Admin)
 */
export const deleteAccessoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate accessory item ID using Zod schema
    const result = accessoryItemIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid accessory item ID', { messages });
    }

    const item = await AccessoryItem.findByIdAndDelete(id);
    
    if (!item) {
      return sendError(res, 404, 'Accessory item not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Accessory item deleted successfully',
      itemId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting accessory item', error);
  }
};

/**
 * @description Search accessory items by name
 * @route GET /api/templates/accessory-items/search
 * @access Protected (Admin, Manager)
 */
export const searchAccessoryItems = async (req: Request, res: Response) => {
  try {
    // Validate search query using Zod schema
    const result = searchAccessoryItemSchema.safeParse({ query: req.query.query });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    const { query } = result.data;

    const items = await AccessoryItem.find({
      itemName: { $regex: query, $options: 'i' }
    }).sort({ itemName: 1 });

    return sendSuccess(res, 200, {
      count: items.length,
      items,
      searchQuery: query
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error searching accessory items', error);
  }
};

