import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import EquipmentTemplate from '../../models/Templates/Equipments/EquipmentTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import {
  createEquipmentTemplateSchema,
  updateEquipmentTemplateSchema,
  templateIdSchema,
  createPredefinedEquipmentTemplateSchema,
  updatePredefinedEquipmentTemplateSchema
} from './templates.validation';

/**
 * @description Get all equipment templates
 * @route GET /api/templates/equipment
 * @access Protected (Admin, Manager)
 */
export const getAllEquipmentTemplates = async (req: Request, res: Response) => {
  try {
    const templates = await EquipmentTemplate.find()
      .populate('items')
      .sort({ createdAt: -1 });

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching equipment templates', error);
  }
};

/**
 * @description Get a single equipment template by ID
 * @route GET /api/templates/equipment/:id
 * @access Protected (Admin, Manager)
 */
export const getEquipmentTemplateById = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate template ID using Zod schema
    const result = templateIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid template ID', { messages });
    }

    const template = await EquipmentTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Equipment template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching equipment template', error);
  }
};

/**
 * @description Create a new equipment template
 * @route POST /api/templates/equipment
 * @access Protected (Admin, Manager)
 */
export const createEquipmentTemplate = async (req: Request, res: Response) => {  try {
    // Validate request body using Zod schema
    const result = createEquipmentTemplateSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }    const { templateName, description, items = [] } = result.data;

    const newTemplate = await EquipmentTemplate.create({
      templateName,
      description,
      items,
    });

    // Populate items for response
    const populatedTemplate = await EquipmentTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, { 
      message: 'Equipment template created successfully', 
      template: populatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating equipment template', error);
  }
};

/**
 * @description Update an equipment template by ID
 * @route PUT /api/templates/equipment/:id
 * @access Protected (Admin, Manager)
 */
export const updateEquipmentTemplate = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate template ID using Zod schema
    const idValidation = templateIdSchema.safeParse({ id });
    
    if (!idValidation.success) {
      const messages = idValidation.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid template ID', { messages });
    }

    // Validate request body using Zod schema
    const result = updateEquipmentTemplateSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    // Check if the template exists
    const existingTemplate = await EquipmentTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Equipment template not found');
    }

    const updateData = result.data;

    const updatedTemplate = await EquipmentTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, { 
      message: 'Equipment template updated successfully', 
      template: updatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating equipment template', error);
  }
};

/**
 * @description Delete an equipment template by ID
 * @route DELETE /api/templates/equipment/:id
 * @access Protected (Admin)
 */
export const deleteEquipmentTemplate = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate template ID using Zod schema
    const result = templateIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid template ID', { messages });
    }

    const template = await EquipmentTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Equipment template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Equipment template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting equipment template', error);
  }
};

