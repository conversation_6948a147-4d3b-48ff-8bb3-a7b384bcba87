import { Router } from 'express';
import {
  getAllAccessoryTemplates,
  getAccessoryTemplateById,
  createAccessoryTemplate,
  updateAccessoryTemplate,
  deleteAccessoryTemplate,
} from '../../controllers/Templates/accessory.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const router = Router();

// Accessory templates routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllAccessoryTemplates)
  .post(restrictTo('admin', 'manager'), createAccessoryTemplate);

// Predefined templates routes
router.route('/predefined')
  .get(restrictTo('admin', 'manager'), (req, res) => {
    req.query.isPredefined = 'true';
    getAllAccessoryTemplates(req, res);
  })
  .post(restrictTo('admin', 'manager'), (req, res) => {
    req.query.isPredefined = 'true';
    createAccessoryTemplate(req, res);
  });

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getAccessoryTemplateById)
  .put(restrictTo('admin', 'manager'), updateAccessoryTemplate)
  .delete(restrictTo('admin'), deleteAccessoryTemplate);

export default router;
