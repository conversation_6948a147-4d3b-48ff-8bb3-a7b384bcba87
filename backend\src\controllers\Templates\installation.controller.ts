import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import InstallationTemplate from '../../models/Templates/Installation/InstallationTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createInstallationTemplateSchema, 
  updateInstallationTemplateSchema, 
  templateIdSchema,
  createPredefinedInstallationTemplateSchema,
  updatePredefinedInstallationTemplateSchema
} from './templates.validation';

/**
 * @description Get all installation templates
 * @route GET /api/templates/installations
 * @access Protected (Admin, Manager)
 */
export const getAllInstallationTemplates = async (req: Request, res: Response) => {
  try {
    const templates = await InstallationTemplate.find()
      .populate('items')
      .sort({ createdAt: -1 });

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation templates', error);
  }
};

/**
 * @description Get a single installation template by ID
 * @route GET /api/templates/installations/:id
 * @access Protected (Admin, Manager)
 */
export const getInstallationTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await InstallationTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Installation template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation template', error);
  }
};

/**
 * @description Create a new installation template
 * @route POST /api/templates/installations
 * @access Protected (Admin, Manager)
 */
export const createInstallationTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const result = createInstallationTemplateSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }    const { templateName, description, items = [] } = result.data;

    const newTemplate = await InstallationTemplate.create({
      templateName,
      description,
      items,
    });

    // Populate items for response
    const populatedTemplate = await InstallationTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, { 
      message: 'Installation template created successfully', 
      template: populatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating installation template', error);
  }
};

/**
 * @description Update an installation template by ID
 * @route PUT /api/templates/installations/:id
 * @access Protected (Admin, Manager)
 */
export const updateInstallationTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    // Validate request body
    const bodyResult = updateInstallationTemplateSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const updateData = bodyResult.data;

    // Check if the template exists
    const existingTemplate = await InstallationTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Installation template not found');
    }

    const updatedTemplate = await InstallationTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, { 
      message: 'Installation template updated successfully', 
      template: updatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating installation template', error);
  }
};

/**
 * @description Delete an installation template by ID
 * @route DELETE /api/templates/installations/:id
 * @access Protected (Admin)
 */
export const deleteInstallationTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await InstallationTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Installation template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Installation template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting installation template', error);
  }
};

