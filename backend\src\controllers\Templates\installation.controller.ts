import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import InstallationTemplate from '../../models/Templates/Installation/InstallationTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createInstallationTemplateSchema, 
  updateInstallationTemplateSchema, 
  templateIdSchema,
  createPredefinedInstallationTemplateSchema,
  updatePredefinedInstallationTemplateSchema
} from './templates.validation';

/**
 * @description Get all installation templates
 * @route GET /api/templates/installations?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const getAllInstallationTemplates = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Build filter based on isPredefined query parameter
    let filter = {};
    if (isPredefined !== undefined) {
      filter = { isPredefined: isPredefined === 'true' };
    }

    const templates = await InstallationTemplate.find(filter)
      .populate('items')
      .sort({ createdAt: -1 });

    // For predefined templates, return in the format expected by frontend
    if (isPredefined === 'true') {
      return sendSuccess(res, 200, {
        count: templates.length,
        installations: templates,
      });
    }

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation templates', error);
  }
};

/**
 * @description Get a single installation template by ID
 * @route GET /api/templates/installations/:id
 * @access Protected (Admin, Manager)
 */
export const getInstallationTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await InstallationTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Installation template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching installation template', error);
  }
};

/**
 * @description Create a new installation template
 * @route POST /api/templates/installations?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const createInstallationTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;
    const isCreatingPredefined = isPredefined === 'true';

    // Use appropriate validation schema based on isPredefined
    const validationSchema = isCreatingPredefined
      ? createPredefinedInstallationTemplateSchema
      : createInstallationTemplateSchema;

    const result = validationSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let templateData;
    if (isCreatingPredefined) {
      const { name, description, items = [] } = result.data as any;
      templateData = {
        templateName: name, // Map 'name' to 'templateName' for predefined
        description,
        items,
        isPredefined: true,
      };
    } else {
      const { templateName, description, items = [] } = result.data as any;
      templateData = {
        templateName,
        description,
        items,
        isPredefined: false,
      };
    }

    const newTemplate = await InstallationTemplate.create(templateData);

    // Populate items for response
    const populatedTemplate = await InstallationTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, {
      message: 'Installation template created successfully',
      template: populatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating installation template', error);
  }
};

/**
 * @description Update an installation template by ID
 * @route PUT /api/templates/installations/:id?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const updateInstallationTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;

    // Check if the template exists
    const existingTemplate = await InstallationTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Installation template not found');
    }

    // Use appropriate validation schema based on isPredefined or existing template
    const isUpdatingPredefined = isPredefined === 'true' || existingTemplate.isPredefined;
    const validationSchema = isUpdatingPredefined
      ? updatePredefinedInstallationTemplateSchema
      : updateInstallationTemplateSchema;

    const bodyResult = validationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let updateData;
    if (isUpdatingPredefined) {
      const { name, description, items } = bodyResult.data as any;
      updateData = {
        ...(name && { templateName: name }), // Map 'name' to 'templateName' for predefined
        ...(description !== undefined && { description }),
        ...(items && { items }),
      };
    } else {
      updateData = bodyResult.data;
    }

    const updatedTemplate = await InstallationTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, {
      message: 'Installation template updated successfully',
      template: updatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating installation template', error);
  }
};

/**
 * @description Delete an installation template by ID
 * @route DELETE /api/templates/installations/:id
 * @access Protected (Admin)
 */
export const deleteInstallationTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await InstallationTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Installation template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Installation template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting installation template', error);
  }
};

