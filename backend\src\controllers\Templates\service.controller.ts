import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import ServiceTemplate from '../../models/Templates/Services/ServiceTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createServiceTemplateSchema, 
  updateServiceTemplateSchema, 
  templateIdSchema,
  createPredefinedServiceTemplateSchema,
  updatePredefinedServiceTemplateSchema
} from './templates.validation';

/**
 * @description Get all service templates
 * @route GET /api/templates/services?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const getAllServiceTemplates = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Build filter based on isPredefined query parameter
    let filter = {};
    if (isPredefined !== undefined) {
      filter = { isPredefined: isPredefined === 'true' };
    }

    const templates = await ServiceTemplate.find(filter)
      .populate('services')
      .sort({ createdAt: -1 });

    // For predefined templates, return in the format expected by frontend
    if (isPredefined === 'true') {
      return sendSuccess(res, 200, {
        count: templates.length,
        services: templates,
      });
    }

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching service templates', error);
  }
};

/**
 * @description Get a single service template by ID
 * @route GET /api/templates/services/:id
 * @access Protected (Admin, Manager)
 */
export const getServiceTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await ServiceTemplate.findById(id).populate('services');
    
    if (!template) {
      return sendError(res, 404, 'Service template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching service template', error);
  }
};

/**
 * @description Create a new service template
 * @route POST /api/templates/services?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const createServiceTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;
    const isCreatingPredefined = isPredefined === 'true';

    // Use appropriate validation schema based on isPredefined
    const validationSchema = isCreatingPredefined
      ? createPredefinedServiceTemplateSchema
      : createServiceTemplateSchema;

    const result = validationSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let templateData;
    if (isCreatingPredefined) {
      const { name, description, items = [] } = result.data as any;
      templateData = {
        templateName: name, // Map 'name' to 'templateName' for predefined
        description,
        services: items, // Map 'items' to 'services' for ServiceTemplate
        isPredefined: true,
      };
    } else {
      const { templateName, description, items = [] } = result.data as any;
      templateData = {
        templateName,
        description,
        services: items, // Map 'items' to 'services' for ServiceTemplate
        isPredefined: false,
      };
    }

    const newTemplate = await ServiceTemplate.create(templateData);

    // Populate services for response
    const populatedTemplate = await ServiceTemplate.findById(newTemplate._id).populate('services');

    return sendSuccess(res, 201, {
      message: 'Service template created successfully',
      template: populatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating service template', error);
  }
};

/**
 * @description Update a service template by ID
 * @route PUT /api/templates/services/:id?isPredefined=true/false
 * @access Protected (Admin, Manager)
 */
export const updateServiceTemplate = async (req: Request, res: Response) => {
  try {
    const { isPredefined } = req.query;

    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;

    // Check if the template exists
    const existingTemplate = await ServiceTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Service template not found');
    }

    // Use appropriate validation schema based on isPredefined or existing template
    const isUpdatingPredefined = isPredefined === 'true' || existingTemplate.isPredefined;
    const validationSchema = isUpdatingPredefined
      ? updatePredefinedServiceTemplateSchema
      : updateServiceTemplateSchema;

    const bodyResult = validationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    let updateData: any;
    if (isUpdatingPredefined) {
      const { name, description, items } = bodyResult.data as any;
      updateData = {
        ...(name && { templateName: name }), // Map 'name' to 'templateName' for predefined
        ...(description !== undefined && { description }),
        ...(items && { services: items }), // Map 'items' to 'services' for ServiceTemplate
      };
    } else {
      updateData = bodyResult.data as any;
      // Map items to services for regular templates too
      if (updateData.items) {
        updateData.services = updateData.items;
        delete updateData.items;
      }
    }

    const updatedTemplate = await ServiceTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('services');

    return sendSuccess(res, 200, {
      message: 'Service template updated successfully',
      template: updatedTemplate
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating service template', error);
  }
};

/**
 * @description Delete a service template by ID
 * @route DELETE /api/templates/services/:id
 * @access Protected (Admin)
 */
export const deleteServiceTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await ServiceTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Service template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Service template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting service template', error);
  }
};

