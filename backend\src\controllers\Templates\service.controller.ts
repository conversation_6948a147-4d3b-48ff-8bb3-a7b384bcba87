import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import ServiceTemplate from '../../models/Templates/Services/ServiceTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createServiceTemplateSchema, 
  updateServiceTemplateSchema, 
  templateIdSchema,
  createPredefinedServiceTemplateSchema,
  updatePredefinedServiceTemplateSchema
} from './templates.validation';

/**
 * @description Get all service templates
 * @route GET /api/templates/services
 * @access Protected (Admin, Manager)
 */
export const getAllServiceTemplates = async (req: Request, res: Response) => {
  try {
    const templates = await ServiceTemplate.find()
      .populate('items')
      .sort({ createdAt: -1 });

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching service templates', error);
  }
};

/**
 * @description Get a single service template by ID
 * @route GET /api/templates/services/:id
 * @access Protected (Admin, Manager)
 */
export const getServiceTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await ServiceTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Service template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching service template', error);
  }
};

/**
 * @description Create a new service template
 * @route POST /api/templates/services
 * @access Protected (Admin, Manager)
 */
export const createServiceTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const result = createServiceTemplateSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }    const { templateName, description, items = [] } = result.data;

    const newTemplate = await ServiceTemplate.create({
      templateName,
      description,
      items,
    });

    // Populate items for response
    const populatedTemplate = await ServiceTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, { 
      message: 'Service template created successfully', 
      template: populatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating service template', error);
  }
};

/**
 * @description Update a service template by ID
 * @route PUT /api/templates/services/:id
 * @access Protected (Admin, Manager)
 */
export const updateServiceTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    // Validate request body
    const bodyResult = updateServiceTemplateSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const updateData = bodyResult.data;

    // Check if the template exists
    const existingTemplate = await ServiceTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Service template not found');
    }

    const updatedTemplate = await ServiceTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, { 
      message: 'Service template updated successfully', 
      template: updatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating service template', error);
  }
};

/**
 * @description Delete a service template by ID
 * @route DELETE /api/templates/services/:id
 * @access Protected (Admin)
 */
export const deleteServiceTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await ServiceTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Service template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Service template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting service template', error);
  }
};

