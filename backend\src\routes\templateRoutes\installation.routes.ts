import { Router } from 'express';
import {
  getAllInstallationTemplates,
  getInstallationTemplateById,
  createInstallationTemplate,
  updateInstallationTemplate,
  deleteInstallationTemplate,
} from '../../controllers/Templates/installation.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const router = Router();

// Installation templates routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllInstallationTemplates)
  .post(restrictTo('admin', 'manager'), createInstallationTemplate);

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getInstallationTemplateById)
  .put(restrictTo('admin', 'manager'), updateInstallationTemplate)
  .delete(restrictTo('admin'), deleteInstallationTemplate);

export default router;
