import { Request, Response, NextFunction } from 'express';
import morgan from 'morgan';
import { ApiResponse, ErrorResponse } from '../types';

// Error handling middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response<ErrorResponse>,
  next: NextFunction
): void => {
  console.error('Error:', error);

  const statusCode = (error as any).statusCode || 500;
  const message = error.message || 'Internal Server Error';

  res.status(statusCode).json({
    error: message,
    statusCode,
    timestamp: new Date().toISOString(),
  });
};

// Morgan request logging middleware
// Custom format for development
const morganFormat = process.env.NODE_ENV === 'production' 
  ? 'combined' 
  : ':method :url :status :res[content-length] - :response-time ms [:date[iso]]';

export const requestLogger = morgan(morganFormat, {
  // Skip logging for health check endpoint to reduce noise
  skip: (req: Request) => req.url === '/health',
  // Custom stream for better formatting
  stream: {
    write: (message: string) => {
      // Remove trailing newline and add custom formatting
      console.log(`🌐 ${message.trim()}`);
    }
  }
});

// CORS configuration
export const corsOptions = {
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
};

// JSON response helper
export const sendResponse = <T>(
  res: Response,
  statusCode: number,
  data?: T,
  message?: string
): void => {
  const response: ApiResponse<T> = {
    success: statusCode < 400,
    data,
    message,
  };

  res.status(statusCode).json(response);
};